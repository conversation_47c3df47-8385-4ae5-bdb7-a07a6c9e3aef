<template>
  <el-drawer title="Advanced settings" v-model="drawerVisible" :direction="drawerDirection" :size="screenIsPortrait ? '50%' : '30%'">
    <el-divider class="el-divider--nowrap">Select Tag Generator</el-divider>
    <el-select v-model="selectedModel" placeholder="Select Tagger">
      <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
    <el-divider class="el-divider--nowrap">Customize API's URL</el-divider>
    <el-input v-model="apiUrl" placeholder="Enter API URL"></el-input>
    <el-input v-model="apiAllTagsUrl" placeholder="Enter API URL for getting the tag pool."></el-input>

    <el-divider class="el-divider--nowrap">UI Options</el-divider>
    <el-checkbox v-model="enhancedSearchTagDisplay" border>
      Show counts and categories in tag searching
    </el-checkbox>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { El<PERSON><PERSON><PERSON>, ElDivider, ElSelect, ElOption, ElInput, ElCheckbox } from 'element-plus'

// Internal state management
const drawerVisible = ref(false)
const screenIsPortrait = ref(window.innerHeight > window.innerWidth)

// Settings state with default values
const selectedModel = ref('gpt-4o-mini')
const modelOptions = ref([
  { value: 'gpt-4o-mini', label: 'gpt-4o-mini (6~7s/item)' },
  { value: 'pytextrank', label: 'pytextrank (fast but low quality)' }
  // Add more model options here
])
const apiUrl = ref('http://127.0.0.1:5000/api/generate_tags')
const apiAllTagsUrl = ref('http://127.0.0.1:5000/api/tags/all')
// const apiUrl = ref('https://tagger.yizehu.com/api/generate_tags')
// const apiAllTagsUrl = ref('https://tagger.yizehu.com/api/tags/all')

// Enhanced tag display setting
const enhancedSearchTagDisplay = ref(true)

// Emits for communicating with parent
const emit = defineEmits([
  'settingsChanged',
  'drawerToggle'
])

// Computed properties
const drawerDirection = computed(() => {
  return screenIsPortrait.value ? 'btt' : 'rtl' // 'btt' = bottom, 'rtl' = right
})

// Business logic: Screen orientation detection
const updateScreenOrientation = () => {
  const newOrientation = window.innerHeight > window.innerWidth
  if (screenIsPortrait.value !== newOrientation) {
    screenIsPortrait.value = newOrientation
    emitSettingsToParent()
  }
}

// Methods
const emitSettingsToParent = () => {
  emit('settingsChanged', {
    selectedModel: selectedModel.value,
    apiUrl: apiUrl.value,
    apiAllTagsUrl: apiAllTagsUrl.value,
    screenIsPortrait: screenIsPortrait.value,
    enhancedSearchTagDisplay: enhancedSearchTagDisplay.value
  })
}

const toggleDrawer = () => {
  drawerVisible.value = !drawerVisible.value
  emit('drawerToggle', drawerVisible.value)
}

// Watch for settings changes and emit to parent
watch([selectedModel, apiUrl, apiAllTagsUrl, enhancedSearchTagDisplay], () => {
  emitSettingsToParent()
}, { deep: true })

// Lifecycle hooks for screen orientation handling
onMounted(() => {
  window.addEventListener('resize', updateScreenOrientation)
  // Emit initial settings to parent
  emitSettingsToParent()
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenOrientation)
})

// Expose methods and computed properties for parent component
defineExpose({
  toggleDrawer,
  drawerVisible: computed(() => drawerVisible.value),
  screenIsPortrait: computed(() => screenIsPortrait.value),
  selectedModel: computed(() => selectedModel.value),
  apiUrl: computed(() => apiUrl.value),
  apiAllTagsUrl: computed(() => apiAllTagsUrl.value),
  enhancedSearchTagDisplay: computed(() => enhancedSearchTagDisplay.value)
})
</script>

<style scoped>
/* Component-specific styles can be added here if needed */
</style>
